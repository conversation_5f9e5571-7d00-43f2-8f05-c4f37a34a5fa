# MIDI-Maestro for Jump Up DnB - Prototype v0.0.1

A command-line MIDI generator for creating Jump Up Drum & Bass track templates.

## Features

- **Tempo Control**: User-specified BPM input with validation
- **Track Markers**: Automatic placement of Intro, Drop, and Outro markers
- **Basic Patterns**: 
  - KICK track: C1 notes on beat 1 of bars 1-4
  - SNARE track: D1 notes on beats 2 & 4 of bars 1-4
- **Standard MIDI**: Compatible with all major DAWs

## Quick Start

### Prerequisites

- Python 3.6 or higher
- pip (Python package installer)

### Installation

1. **Clone or download** this project to your local machine
2. **Navigate** to the project directory:
   ```bash
   cd DNB_GENERATOR_2005
   ```
3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

### Usage

1. **Run the generator**:
   ```bash
   python generator_v001.py
   ```
2. **Enter your desired BPM** when prompted (e.g., 174)
3. **Open the generated file** `jumpup_template_v001.mid` in your DAW

### Generated MIDI Structure

- **Track 0**: Tempo & Markers
  - Tempo information
  - Time signature (4/4)
  - Markers: Intro (Bar 1), Drop (Bar 5), Outro (Bar 9)

- **Track 1**: KICK (MIDI Channel 1)
  - Note: C1 (MIDI Note 36)
  - Pattern: Beat 1 of bars 1-4
  - Velocity: 120

- **Track 2**: SNARE (MIDI Channel 2)
  - Note: D1 (MIDI Note 38)
  - Pattern: Beats 2 & 4 of bars 1-4
  - Velocity: 110

### Next Steps

1. Load the MIDI file into your DAW (Cubase, Ableton, FL Studio, etc.)
2. Assign appropriate drum sounds to the KICK and SNARE tracks
3. Use the markers to structure your track
4. Build upon this template with additional elements

## Technical Details

- **MIDI Standard**: Type 1 MIDI file
- **Ticks per beat**: 480
- **Note duration**: 1/16th notes
- **Time signature**: 4/4

## Troubleshooting

- **"ModuleNotFoundError: No module named 'mido'"**: Run `pip install mido`
- **Permission errors**: Ensure you have write permissions in the current directory
- **Invalid BPM**: Enter a positive number between 60-300

## Version History

- **v0.0.1**: Initial CLI prototype with basic KICK/SNARE patterns and markers
