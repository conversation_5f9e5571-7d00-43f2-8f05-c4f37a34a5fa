#!/usr/bin/env python3
"""
Test script for GUI v0.2
"""

import sys
import os

def test_gui_imports():
    """Test if GUI can be imported"""
    print("🧙‍♂️ Testing GUI imports...")
    
    try:
        print("📦 Testing PyQt5 import...")
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5 imported successfully!")
        
        print("📦 Testing GUI module import...")
        import midi_maestro_gui_v0_2
        print("✅ GUI module imported successfully!")
        
        print("📦 Testing MIDI engine import...")
        import midi_engine_v0_1
        print("✅ MIDI engine imported successfully!")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_gui_creation():
    """Test GUI window creation"""
    print("\n🖥️  Testing GUI window creation...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        import midi_maestro_gui_v0_2
        
        # Create application (but don't show window)
        app = QApplication([])
        
        print("📱 Creating main window...")
        window = midi_maestro_gui_v0_2.MIDIMaestroGUI()
        print("✅ Main window created successfully!")
        
        print("🔧 Testing window properties...")
        print(f"   Window title: {window.windowTitle()}")
        print(f"   Window size: {window.size().width()}x{window.size().height()}")
        
        # Clean up
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI creation error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧙‍♂️ MIDI-Maestro GUI Testing Wizard")
    print("=" * 50)
    
    # Test 1: Imports
    if not test_gui_imports():
        print("\n❌ Import tests failed!")
        return False
    
    # Test 2: GUI Creation
    if not test_gui_creation():
        print("\n❌ GUI creation tests failed!")
        return False
    
    print("\n🎉 ALL GUI TESTS PASSED!")
    print("🚀 GUI is ready to run!")
    print("\nTo start the GUI, run:")
    print("   python midi_maestro_gui_v0_2.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
