#!/usr/bin/env python3
"""
MIDI-Maestro for Jump Up DnB - GUI Application v0.2
A PyQt5 interface for the MIDI generation engine.

This GUI provides a user-friendly interface to the core MIDI engine,
allowing users to load configurations and generate MIDI files with ease.

Author: MIDI-Maestro Development Team
Version: 0.2
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, QLabel, QLineEdit, QPushButton, QTextEdit, 
                            QStatusBar, QFileDialog, QMessageBox, QGroupBox,
                            QSpinBox, QComboBox, QProgressBar)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

# Import our MIDI engine
import midi_engine_v0_1 as midi_engine


class MIDIGenerationThread(QThread):
    """Thread for MIDI generation to prevent GUI freezing"""
    finished = pyqtSignal(bool, str, str)  # success, filename, companion_notes
    progress = pyqtSignal(str)  # progress message
    
    def __init__(self, config_file, output_file):
        super().__init__()
        self.config_file = config_file
        self.output_file = output_file
    
    def run(self):
        try:
            self.progress.emit("Loading configuration...")
            song_config, markers, tracks = midi_engine.load_project_config_from_json(self.config_file)
            
            self.progress.emit("Generating MIDI file...")
            success = midi_engine.generate_midi_from_config(song_config, markers, tracks, self.output_file)
            
            if success:
                self.progress.emit("Generating companion notes...")
                companion_notes = midi_engine.generate_companion_notes(song_config, markers, tracks)
                self.finished.emit(True, self.output_file, companion_notes)
            else:
                self.finished.emit(False, self.output_file, "")
                
        except Exception as e:
            self.finished.emit(False, self.output_file, f"Error: {str(e)}")


class MIDIMaestroGUI(QMainWindow):
    """Main GUI application for MIDI-Maestro"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.generation_thread = None
        
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("MIDI-Maestro for Jump Up DnB v0.2")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Title
        title_label = QLabel("🎵 MIDI-Maestro for Jump Up DnB")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # Configuration section
        config_group = QGroupBox("Configuration")
        config_layout = QVBoxLayout(config_group)
        
        # Config file selection
        config_file_layout = QHBoxLayout()
        config_file_layout.addWidget(QLabel("Configuration File:"))
        
        self.config_file_edit = QLineEdit("config.json")
        self.config_file_edit.setReadOnly(True)
        config_file_layout.addWidget(self.config_file_edit)
        
        self.browse_button = QPushButton("Browse...")
        self.browse_button.clicked.connect(self.browse_config_file)
        config_file_layout.addWidget(self.browse_button)
        
        config_layout.addLayout(config_file_layout)
        main_layout.addWidget(config_group)
        
        # Song settings section
        settings_group = QGroupBox("Song Settings")
        settings_layout = QVBoxLayout(settings_group)
        
        # BPM setting
        bpm_layout = QHBoxLayout()
        bpm_layout.addWidget(QLabel("BPM:"))
        self.bpm_spinbox = QSpinBox()
        self.bpm_spinbox.setRange(100, 200)
        self.bpm_spinbox.setValue(174)
        bpm_layout.addWidget(self.bpm_spinbox)
        bpm_layout.addStretch()
        settings_layout.addLayout(bpm_layout)
        
        # Key signature setting
        key_layout = QHBoxLayout()
        key_layout.addWidget(QLabel("Key Signature:"))
        self.key_edit = QLineEdit("F Minor")
        key_layout.addWidget(self.key_edit)
        key_layout.addStretch()
        settings_layout.addLayout(key_layout)
        
        # Song structure template
        structure_layout = QHBoxLayout()
        structure_layout.addWidget(QLabel("Song Structure:"))
        self.structure_combo = QComboBox()
        self.structure_combo.addItems([
            "Classic Jump Up",
            "Minimal Roller", 
            "Hard Jump Up",
            "Liquid DnB",
            "Neurofunk"
        ])
        structure_layout.addWidget(self.structure_combo)
        structure_layout.addStretch()
        settings_layout.addLayout(structure_layout)
        
        main_layout.addWidget(settings_group)
        
        # Generation section
        generation_layout = QHBoxLayout()
        
        self.generate_button = QPushButton("🎵 Generate MIDI & Notes")
        self.generate_button.clicked.connect(self.generate_midi)
        self.generate_button.setMinimumHeight(40)
        generation_layout.addWidget(self.generate_button)
        
        self.open_folder_button = QPushButton("📁 Open Output Folder")
        self.open_folder_button.clicked.connect(self.open_output_folder)
        generation_layout.addWidget(self.open_folder_button)
        
        main_layout.addLayout(generation_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # Companion notes display
        notes_group = QGroupBox("Companion Notes")
        notes_layout = QVBoxLayout(notes_group)
        
        self.notes_text = QTextEdit()
        self.notes_text.setReadOnly(True)
        self.notes_text.setFont(QFont("Consolas", 9))
        self.notes_text.setPlainText("Click 'Generate MIDI & Notes' to see detailed information about your generated track...")
        notes_layout.addWidget(self.notes_text)
        
        main_layout.addWidget(notes_group)
        
        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready to generate MIDI files")
        
        # Load initial config if it exists
        self.load_config_preview()
    
    def browse_config_file(self):
        """Browse for configuration file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "Select Configuration File", 
            "", 
            "JSON Files (*.json);;All Files (*)"
        )
        if file_path:
            self.config_file_edit.setText(file_path)
            self.load_config_preview()
    
    def load_config_preview(self):
        """Load and preview configuration file"""
        config_file = self.config_file_edit.text()
        if os.path.exists(config_file):
            try:
                song_config, markers, tracks = midi_engine.load_project_config_from_json(config_file)
                self.bpm_spinbox.setValue(song_config.bpm)
                self.key_edit.setText(song_config.key_signature)
                self.status_bar.showMessage(f"Loaded config: {len(tracks)} tracks, {len(markers)} markers")
            except Exception as e:
                self.status_bar.showMessage(f"Error loading config: {str(e)}")
        else:
            self.status_bar.showMessage("Configuration file not found")
    
    def generate_midi(self):
        """Generate MIDI file in separate thread"""
        if self.generation_thread and self.generation_thread.isRunning():
            QMessageBox.warning(self, "Generation in Progress", "MIDI generation is already in progress!")
            return
        
        config_file = self.config_file_edit.text()
        if not os.path.exists(config_file):
            QMessageBox.critical(self, "File Not Found", f"Configuration file '{config_file}' not found!")
            return
        
        # Generate output filename
        bpm = self.bpm_spinbox.value()
        key = self.key_edit.text().replace(" ", "_")
        structure = self.structure_combo.currentText().replace(" ", "_")
        output_file = f"jumpup_{structure}_{key}_{bpm}bpm.mid"
        
        # Start generation thread
        self.generation_thread = MIDIGenerationThread(config_file, output_file)
        self.generation_thread.finished.connect(self.on_generation_finished)
        self.generation_thread.progress.connect(self.on_generation_progress)
        
        # Update UI
        self.generate_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        
        self.generation_thread.start()
    
    def on_generation_progress(self, message):
        """Handle progress updates"""
        self.status_bar.showMessage(message)
    
    def on_generation_finished(self, success, filename, companion_notes):
        """Handle generation completion"""
        self.generate_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        if success:
            self.notes_text.setPlainText(companion_notes)
            self.status_bar.showMessage(f"✅ MIDI file '{filename}' generated successfully!")
            
            # Show success message
            QMessageBox.information(
                self, 
                "Generation Complete", 
                f"MIDI file '{filename}' has been generated successfully!\n\n"
                f"You can now import it into your DAW to start producing."
            )
        else:
            self.status_bar.showMessage("❌ MIDI generation failed!")
            QMessageBox.critical(
                self, 
                "Generation Failed", 
                f"Failed to generate MIDI file.\n\nError: {companion_notes}"
            )
    
    def open_output_folder(self):
        """Open the output folder in file explorer"""
        import subprocess
        import platform
        
        folder_path = os.getcwd()
        
        try:
            if platform.system() == "Windows":
                subprocess.run(["explorer", folder_path])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Could not open folder: {str(e)}")


def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    app.setApplicationName("MIDI-Maestro")
    app.setApplicationVersion("0.2")
    
    # Create and show main window
    window = MIDIMaestroGUI()
    window.show()
    
    # Run application
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
