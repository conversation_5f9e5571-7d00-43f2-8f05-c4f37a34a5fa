#!/usr/bin/env python3
"""
MIDI-Maestro for Jump Up DnB - Enhanced Pattern Engine v0.1.0
A sophisticated command-line MIDI generator for Jump Up Drum & Bass track templates.

This script generates professional Standard MIDI Files (.mid) containing:
- Multiple drum pattern variations (kick, snare, hi-hat)
- Bass line generation with multiple patterns
- Interactive pattern selection and customization
- Advanced song structure with multiple sections
- Pattern visualization and export options
- Professional velocity and timing variations

Author: MIDI-Maestro Development Team
Version: 0.1.0
"""

import mido
import sys
import os
import random
import json
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional
from enum import Enum


# ============================================================================
# ENHANCED DATA STRUCTURES AND PATTERN DEFINITIONS
# ============================================================================

class PatternComplexity(Enum):
    """Pattern complexity levels"""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    PROFESSIONAL = "professional"


class DrumSound(Enum):
    """Standard drum sound mappings"""
    KICK = 36          # C1
    SNARE = 38         # D1
    HIHAT_CLOSED = 42  # F#1
    HIHAT_OPEN = 46    # A#1
    CRASH = 49         # C#2
    RIDE = 51          # D#2


@dataclass
class PatternStep:
    """Represents a single step in a drum pattern"""
    beat: float        # Beat position (1.0, 1.25, 1.5, etc.)
    velocity: int      # MIDI velocity (0-127)
    duration: float    # Note duration in beats
    accent: bool = False  # Whether this is an accented hit


@dataclass
class DrumPattern:
    """Complete drum pattern for one instrument"""
    name: str
    steps: List[PatternStep]
    complexity: PatternComplexity
    description: str


@dataclass
class BassNote:
    """Bass note with pitch and timing"""
    beat: float        # Beat position
    note: int         # MIDI note number
    velocity: int     # MIDI velocity
    duration: float   # Note duration in beats


@dataclass
class BassPattern:
    """Complete bass pattern"""
    name: str
    notes: List[BassNote]
    complexity: PatternComplexity
    description: str
    root_note: int = 36  # Default to C1


# ============================================================================
# PATTERN LIBRARY - AUTHENTIC JUMP UP DNB PATTERNS
# ============================================================================

# KICK PATTERNS
KICK_PATTERNS = {
    PatternComplexity.BEGINNER: [
        DrumPattern(
            name="Classic Four-on-Floor",
            steps=[
                PatternStep(1.0, 120, 0.25),
                PatternStep(2.0, 120, 0.25),
                PatternStep(3.0, 120, 0.25),
                PatternStep(4.0, 120, 0.25),
            ],
            complexity=PatternComplexity.BEGINNER,
            description="Simple kick on every beat - great for beginners"
        ),
        DrumPattern(
            name="Basic Jump Up",
            steps=[
                PatternStep(1.0, 127, 0.25, accent=True),
                PatternStep(3.0, 110, 0.25),
            ],
            complexity=PatternComplexity.BEGINNER,
            description="Classic jump up kick pattern - beats 1 and 3"
        ),
    ],

    PatternComplexity.INTERMEDIATE: [
        DrumPattern(
            name="Syncopated Jump",
            steps=[
                PatternStep(1.0, 127, 0.25, accent=True),
                PatternStep(2.75, 100, 0.125),
                PatternStep(3.0, 115, 0.25),
                PatternStep(4.5, 105, 0.125),
            ],
            complexity=PatternComplexity.INTERMEDIATE,
            description="Syncopated pattern with off-beat kicks"
        ),
        DrumPattern(
            name="Double Kick",
            steps=[
                PatternStep(1.0, 127, 0.25, accent=True),
                PatternStep(1.75, 90, 0.125),
                PatternStep(3.0, 120, 0.25),
            ],
            complexity=PatternComplexity.INTERMEDIATE,
            description="Double kick with ghost note"
        ),
    ],

    PatternComplexity.ADVANCED: [
        DrumPattern(
            name="Complex Syncopation",
            steps=[
                PatternStep(1.0, 127, 0.25, accent=True),
                PatternStep(1.625, 85, 0.125),
                PatternStep(2.75, 95, 0.125),
                PatternStep(3.0, 120, 0.25),
                PatternStep(3.875, 90, 0.125),
                PatternStep(4.5, 100, 0.125),
            ],
            complexity=PatternComplexity.ADVANCED,
            description="Complex syncopated pattern with multiple ghost notes"
        ),
    ]
}

# SNARE PATTERNS
SNARE_PATTERNS = {
    PatternComplexity.BEGINNER: [
        DrumPattern(
            name="Classic Backbeat",
            steps=[
                PatternStep(2.0, 110, 0.25),
                PatternStep(4.0, 110, 0.25),
            ],
            complexity=PatternComplexity.BEGINNER,
            description="Traditional snare on beats 2 and 4"
        ),
    ],

    PatternComplexity.INTERMEDIATE: [
        DrumPattern(
            name="Jump Up Snare",
            steps=[
                PatternStep(2.0, 120, 0.25, accent=True),
                PatternStep(3.75, 95, 0.125),
                PatternStep(4.0, 115, 0.25),
            ],
            complexity=PatternComplexity.INTERMEDIATE,
            description="Classic jump up snare with ghost note"
        ),
    ],

    PatternComplexity.ADVANCED: [
        DrumPattern(
            name="Rapid Fire",
            steps=[
                PatternStep(2.0, 127, 0.25, accent=True),
                PatternStep(2.5, 85, 0.125),
                PatternStep(2.75, 90, 0.125),
                PatternStep(3.75, 95, 0.125),
                PatternStep(4.0, 120, 0.25, accent=True),
                PatternStep(4.625, 80, 0.125),
            ],
            complexity=PatternComplexity.ADVANCED,
            description="Rapid-fire snare pattern with multiple ghost notes"
        ),
    ]
}

# HI-HAT PATTERNS
HIHAT_PATTERNS = {
    PatternComplexity.BEGINNER: [
        DrumPattern(
            name="Simple 8th Notes",
            steps=[
                PatternStep(1.0, 80, 0.125),
                PatternStep(1.5, 70, 0.125),
                PatternStep(2.0, 80, 0.125),
                PatternStep(2.5, 70, 0.125),
                PatternStep(3.0, 80, 0.125),
                PatternStep(3.5, 70, 0.125),
                PatternStep(4.0, 80, 0.125),
                PatternStep(4.5, 70, 0.125),
            ],
            complexity=PatternComplexity.BEGINNER,
            description="Simple 8th note hi-hat pattern"
        ),
    ],

    PatternComplexity.INTERMEDIATE: [
        DrumPattern(
            name="Swing Hi-Hats",
            steps=[
                PatternStep(1.0, 85, 0.125),
                PatternStep(1.375, 65, 0.125),
                PatternStep(1.75, 75, 0.125),
                PatternStep(2.0, 80, 0.125),
                PatternStep(2.375, 60, 0.125),
                PatternStep(2.75, 70, 0.125),
                PatternStep(3.0, 85, 0.125),
                PatternStep(3.375, 65, 0.125),
                PatternStep(3.75, 75, 0.125),
                PatternStep(4.0, 80, 0.125),
                PatternStep(4.375, 60, 0.125),
                PatternStep(4.75, 70, 0.125),
            ],
            complexity=PatternComplexity.INTERMEDIATE,
            description="Swung hi-hat pattern with velocity variations"
        ),
    ]
}

# BASS PATTERNS
BASS_PATTERNS = {
    PatternComplexity.BEGINNER: [
        BassPattern(
            name="Simple Root",
            notes=[
                BassNote(1.0, 36, 100, 0.5),  # C1
                BassNote(3.0, 36, 95, 0.5),
            ],
            complexity=PatternComplexity.BEGINNER,
            description="Simple root note pattern",
            root_note=36
        ),
    ],

    PatternComplexity.INTERMEDIATE: [
        BassPattern(
            name="Jump Up Bass",
            notes=[
                BassNote(1.0, 36, 110, 0.25),   # C1
                BassNote(1.75, 43, 85, 0.125),  # G1
                BassNote(2.5, 36, 100, 0.25),   # C1
                BassNote(3.0, 39, 105, 0.5),    # D#1
                BassNote(4.0, 36, 110, 0.25),   # C1
            ],
            complexity=PatternComplexity.INTERMEDIATE,
            description="Classic jump up bass line with fifths",
            root_note=36
        ),
    ],

    PatternComplexity.ADVANCED: [
        BassPattern(
            name="Wobble Bass",
            notes=[
                BassNote(1.0, 36, 120, 0.125),   # C1
                BassNote(1.125, 37, 90, 0.125),  # C#1
                BassNote(1.25, 36, 110, 0.125),  # C1
                BassNote(1.5, 43, 95, 0.125),    # G1
                BassNote(1.75, 36, 105, 0.125),  # C1
                BassNote(2.0, 39, 100, 0.25),    # D#1
                BassNote(2.5, 36, 115, 0.125),   # C1
                BassNote(2.75, 43, 85, 0.125),   # G1
                BassNote(3.0, 36, 120, 0.25),    # C1
                BassNote(3.5, 39, 95, 0.125),    # D#1
                BassNote(3.75, 36, 100, 0.125),  # C1
                BassNote(4.0, 43, 110, 0.25),    # G1
            ],
            complexity=PatternComplexity.ADVANCED,
            description="Complex wobble bass with chromatic movement",
            root_note=36
        ),
    ]
}


# ============================================================================
# ENHANCED USER INTERFACE FUNCTIONS
# ============================================================================

def display_banner():
    """Display the enhanced application banner"""
    print("=" * 70)
    print("🎵  MIDI-MAESTRO for Jump Up DnB - Enhanced Pattern Engine v0.1.0  🎵")
    print("=" * 70)
    print("Professional MIDI generation for Jump Up Drum & Bass production")
    print("Features: Multiple patterns • Bass lines • Advanced timing • Export options")
    print("=" * 70)
    print()


def get_user_bpm():
    """Enhanced BPM input with suggestions for Jump Up DnB"""
    print("🎵 BPM Selection")
    print("Typical Jump Up DnB BPM ranges:")
    print("  • Classic Jump Up: 170-176 BPM")
    print("  • Modern Jump Up: 174-180 BPM")
    print("  • Hard Jump Up: 178-185 BPM")
    print()

    while True:
        try:
            bpm_input = input("Enter BPM for your track (recommended: 174): ").strip()
            if not bpm_input:
                bpm = 174  # Default
                print(f"Using default BPM: {bpm}")
                return bpm

            bpm = float(bpm_input)

            if bpm <= 0:
                print("❌ Error: BPM must be a positive number.")
                continue
            if bpm < 60 or bpm > 300:
                print("⚠️  Warning: BPM outside typical range (60-300). Continue anyway? (y/n)")
                confirm = input().lower().strip()
                if confirm not in ['y', 'yes']:
                    continue

            return int(bpm)

        except ValueError:
            print("❌ Error: Please enter a valid number for BPM.")


def get_complexity_level():
    """Get user's preferred pattern complexity level"""
    print("\n🎯 Pattern Complexity Selection")
    print("Choose your skill level and desired pattern complexity:")
    print()
    print("1. BEGINNER     - Simple, easy-to-follow patterns")
    print("2. INTERMEDIATE - Classic Jump Up patterns with some syncopation")
    print("3. ADVANCED     - Complex patterns with ghost notes and syncopation")
    print()

    while True:
        try:
            choice = input("Select complexity level (1-3) [default: 2]: ").strip()
            if not choice:
                return PatternComplexity.INTERMEDIATE

            choice_num = int(choice)
            if choice_num == 1:
                return PatternComplexity.BEGINNER
            elif choice_num == 2:
                return PatternComplexity.INTERMEDIATE
            elif choice_num == 3:
                return PatternComplexity.ADVANCED
            else:
                print("❌ Please enter 1, 2, or 3")

        except ValueError:
            print("❌ Please enter a valid number (1-3)")


def select_patterns(complexity: PatternComplexity):
    """Allow user to select specific patterns for each instrument"""
    print(f"\n🥁 Pattern Selection for {complexity.value.upper()} level")
    print()

    # Select kick pattern
    kick_patterns = KICK_PATTERNS[complexity]
    print("Available KICK patterns:")
    for i, pattern in enumerate(kick_patterns, 1):
        print(f"  {i}. {pattern.name} - {pattern.description}")

    while True:
        try:
            choice = input(f"Select kick pattern (1-{len(kick_patterns)}) [default: 1]: ").strip()
            if not choice:
                selected_kick = kick_patterns[0]
                break
            choice_num = int(choice)
            if 1 <= choice_num <= len(kick_patterns):
                selected_kick = kick_patterns[choice_num - 1]
                break
            else:
                print(f"❌ Please enter a number between 1 and {len(kick_patterns)}")
        except ValueError:
            print("❌ Please enter a valid number")

    # Select snare pattern
    snare_patterns = SNARE_PATTERNS[complexity]
    print(f"\nAvailable SNARE patterns:")
    for i, pattern in enumerate(snare_patterns, 1):
        print(f"  {i}. {pattern.name} - {pattern.description}")

    while True:
        try:
            choice = input(f"Select snare pattern (1-{len(snare_patterns)}) [default: 1]: ").strip()
            if not choice:
                selected_snare = snare_patterns[0]
                break
            choice_num = int(choice)
            if 1 <= choice_num <= len(snare_patterns):
                selected_snare = snare_patterns[choice_num - 1]
                break
            else:
                print(f"❌ Please enter a number between 1 and {len(snare_patterns)}")
        except ValueError:
            print("❌ Please enter a valid number")

    # Select hi-hat pattern
    hihat_patterns = HIHAT_PATTERNS.get(complexity, [])
    selected_hihat = None
    if hihat_patterns:
        print(f"\nAvailable HI-HAT patterns:")
        for i, pattern in enumerate(hihat_patterns, 1):
            print(f"  {i}. {pattern.name} - {pattern.description}")
        print(f"  {len(hihat_patterns) + 1}. None - Skip hi-hat track")

        while True:
            try:
                choice = input(f"Select hi-hat pattern (1-{len(hihat_patterns) + 1}) [default: 1]: ").strip()
                if not choice:
                    selected_hihat = hihat_patterns[0] if hihat_patterns else None
                    break
                choice_num = int(choice)
                if 1 <= choice_num <= len(hihat_patterns):
                    selected_hihat = hihat_patterns[choice_num - 1]
                    break
                elif choice_num == len(hihat_patterns) + 1:
                    selected_hihat = None
                    break
                else:
                    print(f"❌ Please enter a number between 1 and {len(hihat_patterns) + 1}")
            except ValueError:
                print("❌ Please enter a valid number")

    # Select bass pattern
    bass_patterns = BASS_PATTERNS.get(complexity, [])
    selected_bass = None
    if bass_patterns:
        print(f"\nAvailable BASS patterns:")
        for i, pattern in enumerate(bass_patterns, 1):
            print(f"  {i}. {pattern.name} - {pattern.description}")
        print(f"  {len(bass_patterns) + 1}. None - Skip bass track")

        while True:
            try:
                choice = input(f"Select bass pattern (1-{len(bass_patterns) + 1}) [default: 1]: ").strip()
                if not choice:
                    selected_bass = bass_patterns[0] if bass_patterns else None
                    break
                choice_num = int(choice)
                if 1 <= choice_num <= len(bass_patterns):
                    selected_bass = bass_patterns[choice_num - 1]
                    break
                elif choice_num == len(bass_patterns) + 1:
                    selected_bass = None
                    break
                else:
                    print(f"❌ Please enter a number between 1 and {len(bass_patterns) + 1}")
            except ValueError:
                print("❌ Please enter a valid number")

    return {
        'kick': selected_kick,
        'snare': selected_snare,
        'hihat': selected_hihat,
        'bass': selected_bass
    }


def bar_beat_to_ticks(bar, beat, ticks_per_beat=480, beats_per_bar=4):
    """
    Converts bar/beat position to absolute MIDI ticks.

    Args:
        bar (int): Bar number (1-based)
        beat (float): Beat position (1.0, 1.25, 1.5, etc.)
        ticks_per_beat (int): MIDI ticks per quarter note
        beats_per_bar (int): Beats per bar (4 for 4/4 time)

    Returns:
        int: Absolute tick position
    """
    # Convert to 0-based indexing
    bar_zero_based = bar - 1
    beat_zero_based = beat - 1.0

    # Calculate total ticks
    total_ticks = (bar_zero_based * beats_per_bar * ticks_per_beat) + (beat_zero_based * ticks_per_beat)
    return int(total_ticks)


def beat_to_ticks(beat, ticks_per_beat=480):
    """Convert beat position to ticks (for use within a single bar)"""
    return int((beat - 1.0) * ticks_per_beat)


# ============================================================================
# ENHANCED PATTERN GENERATION FUNCTIONS
# ============================================================================

def create_pattern_track(pattern: DrumPattern, drum_sound: DrumSound, channel: int,
                        bars: int = 4, ticks_per_beat: int = 480) -> mido.MidiTrack:
    """
    Create a MIDI track from a drum pattern.

    Args:
        pattern: DrumPattern object containing the pattern steps
        drum_sound: DrumSound enum for the MIDI note number
        channel: MIDI channel (0-15)
        bars: Number of bars to generate
        ticks_per_beat: MIDI ticks per quarter note

    Returns:
        mido.MidiTrack: Generated MIDI track
    """
    track = mido.MidiTrack()

    # Track name
    track.append(mido.MetaMessage('track_name', name=pattern.name, time=0))

    current_time = 0

    for bar in range(1, bars + 1):
        for step in pattern.steps:
            # Calculate absolute tick position
            note_start_tick = bar_beat_to_ticks(bar, step.beat, ticks_per_beat)
            note_duration_ticks = int(step.duration * ticks_per_beat)

            # Delta time to note start
            delta_to_start = note_start_tick - current_time

            # Note ON
            track.append(mido.Message('note_on',
                                    channel=channel,
                                    note=drum_sound.value,
                                    velocity=step.velocity,
                                    time=delta_to_start))

            # Note OFF
            track.append(mido.Message('note_off',
                                    channel=channel,
                                    note=drum_sound.value,
                                    velocity=0,
                                    time=note_duration_ticks))

            current_time = note_start_tick + note_duration_ticks

    # End of track
    track.append(mido.MetaMessage('end_of_track', time=0))

    return track


def create_bass_track(pattern: BassPattern, channel: int,
                     bars: int = 4, ticks_per_beat: int = 480) -> mido.MidiTrack:
    """
    Create a MIDI track from a bass pattern.

    Args:
        pattern: BassPattern object containing the bass notes
        channel: MIDI channel (0-15)
        bars: Number of bars to generate
        ticks_per_beat: MIDI ticks per quarter note

    Returns:
        mido.MidiTrack: Generated MIDI track
    """
    track = mido.MidiTrack()

    # Track name
    track.append(mido.MetaMessage('track_name', name=pattern.name, time=0))

    current_time = 0

    for bar in range(1, bars + 1):
        for note in pattern.notes:
            # Calculate absolute tick position
            note_start_tick = bar_beat_to_ticks(bar, note.beat, ticks_per_beat)
            note_duration_ticks = int(note.duration * ticks_per_beat)

            # Delta time to note start
            delta_to_start = note_start_tick - current_time

            # Note ON
            track.append(mido.Message('note_on',
                                    channel=channel,
                                    note=note.note,
                                    velocity=note.velocity,
                                    time=delta_to_start))

            # Note OFF
            track.append(mido.Message('note_off',
                                    channel=channel,
                                    note=note.note,
                                    velocity=0,
                                    time=note_duration_ticks))

            current_time = note_start_tick + note_duration_ticks

    # End of track
    track.append(mido.MetaMessage('end_of_track', time=0))

    return track


def create_enhanced_tempo_and_markers_track(bpm, total_bars=16, ticks_per_beat=480):
    """
    Creates an enhanced tempo and markers track with professional song structure.

    Args:
        bpm (int): Beats per minute
        total_bars (int): Total number of bars in the track
        ticks_per_beat (int): MIDI ticks per quarter note

    Returns:
        mido.MidiTrack: Configured tempo and markers track
    """
    track = mido.MidiTrack()

    # Track name
    track.append(mido.MetaMessage('track_name', name='Tempo & Markers', time=0))

    # Set tempo (microseconds per beat)
    tempo = mido.bpm2tempo(bpm)
    track.append(mido.MetaMessage('set_tempo', tempo=tempo, time=0))

    # Set time signature (4/4)
    track.append(mido.MetaMessage('time_signature', numerator=4, denominator=4,
                                 clocks_per_click=24, notated_32nd_notes_per_beat=8, time=0))

    # Enhanced marker structure for Jump Up DnB
    markers = [
        (1, 'Intro'),
        (5, 'Build Up'),
        (9, 'Drop 1'),
        (13, 'Breakdown'),
        (17, 'Drop 2') if total_bars >= 17 else None,
        (21, 'Outro') if total_bars >= 21 else (total_bars - 3, 'Outro') if total_bars > 4 else None
    ]

    # Filter out None markers and adjust for actual track length
    markers = [(bar, name) for bar, name in markers if bar is not None and bar <= total_bars]

    current_time = 0

    for bar, marker_name in markers:
        marker_tick = bar_beat_to_ticks(bar, 1, ticks_per_beat)
        delta_time = marker_tick - current_time
        track.append(mido.MetaMessage('marker', text=marker_name, time=delta_time))
        current_time = marker_tick

    # End of track
    track.append(mido.MetaMessage('end_of_track', time=0))

    return track


def display_pattern_summary(selected_patterns, bpm):
    """Display a summary of selected patterns"""
    print(f"\n🎼 Pattern Summary (BPM: {bpm})")
    print("=" * 50)

    if selected_patterns['kick']:
        print(f"🥁 KICK: {selected_patterns['kick'].name}")
        print(f"   └─ {selected_patterns['kick'].description}")

    if selected_patterns['snare']:
        print(f"🥁 SNARE: {selected_patterns['snare'].name}")
        print(f"   └─ {selected_patterns['snare'].description}")

    if selected_patterns['hihat']:
        print(f"🎩 HI-HAT: {selected_patterns['hihat'].name}")
        print(f"   └─ {selected_patterns['hihat'].description}")

    if selected_patterns['bass']:
        print(f"🎸 BASS: {selected_patterns['bass'].name}")
        print(f"   └─ {selected_patterns['bass'].description}")

    print("=" * 50)


def generate_enhanced_midi_file(bpm, selected_patterns, bars=16, filename=None):
    """
    Generate an enhanced MIDI file with multiple patterns and professional structure.

    Args:
        bpm (int): Beats per minute
        selected_patterns (dict): Dictionary of selected patterns
        bars (int): Number of bars to generate
        filename (str): Output filename (auto-generated if None)

    Returns:
        tuple: (success: bool, filename: str)
    """
    try:
        if filename is None:
            complexity = selected_patterns['kick'].complexity.value if selected_patterns['kick'] else 'custom'
            filename = f"jumpup_enhanced_{complexity}_{bpm}bpm.mid"

        # Create MIDI file with standard settings
        ticks_per_beat = 480
        mid = mido.MidiFile(ticks_per_beat=ticks_per_beat)

        # Create tempo and markers track
        tempo_track = create_enhanced_tempo_and_markers_track(bpm, bars, ticks_per_beat)
        mid.tracks.append(tempo_track)

        # Create drum tracks
        channel = 0
        if selected_patterns['kick']:
            kick_track = create_pattern_track(
                selected_patterns['kick'],
                DrumSound.KICK,
                channel,
                bars,
                ticks_per_beat
            )
            mid.tracks.append(kick_track)
            channel += 1

        if selected_patterns['snare']:
            snare_track = create_pattern_track(
                selected_patterns['snare'],
                DrumSound.SNARE,
                channel,
                bars,
                ticks_per_beat
            )
            mid.tracks.append(snare_track)
            channel += 1

        if selected_patterns['hihat']:
            hihat_track = create_pattern_track(
                selected_patterns['hihat'],
                DrumSound.HIHAT_CLOSED,
                channel,
                bars,
                ticks_per_beat
            )
            mid.tracks.append(hihat_track)
            channel += 1

        # Create bass track
        if selected_patterns['bass']:
            bass_track = create_bass_track(
                selected_patterns['bass'],
                channel,
                bars,
                ticks_per_beat
            )
            mid.tracks.append(bass_track)

        # Save the MIDI file
        mid.save(filename)

        return True, filename

    except Exception as e:
        print(f"❌ Error generating MIDI file: {e}")
        return False, filename


def print_enhanced_companion_notes(bpm, filename, selected_patterns, bars):
    """
    Print enhanced companion notes with detailed pattern information.

    Args:
        bpm (int): BPM used in the generated file
        filename (str): Name of the generated MIDI file
        selected_patterns (dict): Dictionary of selected patterns
        bars (int): Number of bars generated
    """
    print(f"\n✅ MIDI file '{filename}' created successfully!")
    print(f"\n🎼 Enhanced Companion Notes (v0.1.0)")
    print("=" * 60)
    print(f"📊 Track Details:")
    print(f"   • BPM: {bpm}")
    print(f"   • Length: {bars} bars")
    print(f"   • Time Signature: 4/4")
    print(f"   • Ticks per beat: 480")
    print()

    print("🎵 Song Structure:")
    if bars >= 16:
        print("   • Intro (Bar 1-4)")
        print("   • Build Up (Bar 5-8)")
        print("   • Drop 1 (Bar 9-12)")
        print("   • Breakdown (Bar 13-16)")
        if bars >= 20:
            print("   • Drop 2 (Bar 17-20)")
            print("   • Outro (Bar 21+)")
    else:
        print(f"   • Pattern repeats for {bars} bars")
    print()

    print("🥁 Track Information:")
    track_num = 1

    if selected_patterns['kick']:
        print(f"   Track {track_num}: KICK - {selected_patterns['kick'].name}")
        print(f"      └─ MIDI Note: C1 (36) | Channel: {track_num}")
        print(f"      └─ Pattern: {selected_patterns['kick'].description}")
        track_num += 1

    if selected_patterns['snare']:
        print(f"   Track {track_num}: SNARE - {selected_patterns['snare'].name}")
        print(f"      └─ MIDI Note: D1 (38) | Channel: {track_num}")
        print(f"      └─ Pattern: {selected_patterns['snare'].description}")
        track_num += 1

    if selected_patterns['hihat']:
        print(f"   Track {track_num}: HI-HAT - {selected_patterns['hihat'].name}")
        print(f"      └─ MIDI Note: F#1 (42) | Channel: {track_num}")
        print(f"      └─ Pattern: {selected_patterns['hihat'].description}")
        track_num += 1

    if selected_patterns['bass']:
        print(f"   Track {track_num}: BASS - {selected_patterns['bass'].name}")
        print(f"      └─ Root Note: C1 (36) | Channel: {track_num}")
        print(f"      └─ Pattern: {selected_patterns['bass'].description}")

    print()
    print("🎛️  Next Steps:")
    print("   1. Import the MIDI file into your DAW")
    print("   2. Load appropriate Jump Up DnB samples:")
    print("      • Punchy kick drum for the kick track")
    print("      • Crisp snare with good snap for the snare track")
    if selected_patterns['hihat']:
        print("      • Tight hi-hat samples for the hi-hat track")
    if selected_patterns['bass']:
        print("      • Deep sub-bass or reese bass for the bass track")
    print("   3. Use the song markers to structure your arrangement")
    print("   4. Add your own elements: leads, pads, FX, vocals")
    print("   5. Apply compression, EQ, and mastering")
    print()
    print("🚀 Happy producing! This template is your foundation for a banging Jump Up track!")
    print("=" * 60)


def get_track_length():
    """Get desired track length from user"""
    print("\n📏 Track Length Selection")
    print("Choose the length of your track:")
    print("1. Short (8 bars) - Quick sketch/loop")
    print("2. Medium (16 bars) - Standard template")
    print("3. Long (32 bars) - Full arrangement")
    print("4. Custom - Specify your own length")
    print()

    while True:
        try:
            choice = input("Select track length (1-4) [default: 2]: ").strip()
            if not choice:
                return 16

            choice_num = int(choice)
            if choice_num == 1:
                return 8
            elif choice_num == 2:
                return 16
            elif choice_num == 3:
                return 32
            elif choice_num == 4:
                while True:
                    try:
                        custom_bars = int(input("Enter number of bars (4-64): "))
                        if 4 <= custom_bars <= 64:
                            return custom_bars
                        else:
                            print("❌ Please enter a number between 4 and 64")
                    except ValueError:
                        print("❌ Please enter a valid number")
            else:
                print("❌ Please enter 1, 2, 3, or 4")

        except ValueError:
            print("❌ Please enter a valid number (1-4)")


def main():
    """
    Enhanced main orchestration function.
    """
    # Display banner
    display_banner()

    # Get user preferences
    bpm = get_user_bpm()
    complexity = get_complexity_level()
    selected_patterns = select_patterns(complexity)
    bars = get_track_length()

    # Display pattern summary
    display_pattern_summary(selected_patterns, bpm)

    # Confirm generation
    print(f"\n🎯 Ready to generate {bars}-bar Jump Up DnB template at {bpm} BPM")
    confirm = input("Generate MIDI file? (y/n) [default: y]: ").strip().lower()
    if confirm and confirm not in ['y', 'yes']:
        print("❌ Generation cancelled.")
        return

    print("\n🎵 Generating enhanced MIDI file...")

    # Generate MIDI file
    success, filename = generate_enhanced_midi_file(bpm, selected_patterns, bars)

    if success:
        # Print enhanced companion notes
        print_enhanced_companion_notes(bpm, filename, selected_patterns, bars)
    else:
        print("❌ Failed to generate MIDI file. Please check for errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
