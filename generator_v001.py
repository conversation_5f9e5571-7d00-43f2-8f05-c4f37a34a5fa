#!/usr/bin/env python3
"""
MIDI-Maestro for Jump Up DnB - Prototype v0.0.1
A command-line MIDI generator for Jump Up Drum & Bass track templates.

This script generates a basic Standard MIDI File (.mid) containing:
- Tempo and time signature information
- Track markers (Intro, Drop, Outro)
- Basic KICK pattern (C1 on beat 1)
- Basic SNARE pattern (D1 on beats 2 & 4)

Author: MIDI-Maestro Development Team
Version: 0.0.1
"""

import mido
import sys
import os


def get_user_bpm():
    """
    Prompts user for BPM input with basic error handling.
    
    Returns:
        int: Valid BPM value between 60 and 300
    """
    while True:
        try:
            bpm_input = input("Enter BPM for your Jump Up DnB track (e.g., 174): ")
            bpm = float(bpm_input)
            
            if bpm <= 0:
                print("Error: BPM must be a positive number.")
                continue
            if bpm < 60 or bpm > 300:
                print("Warning: BPM outside typical range (60-300). Continue anyway? (y/n)")
                confirm = input().lower()
                if confirm != 'y':
                    continue
            
            return int(bpm)
            
        except ValueError:
            print("Error: Please enter a valid number for BPM.")


def bar_beat_to_ticks(bar, beat, ticks_per_beat=480, beats_per_bar=4):
    """
    Converts bar/beat position to absolute MIDI ticks.
    
    Args:
        bar (int): Bar number (1-based)
        beat (int): Beat number (1-based, 1-4 for 4/4 time)
        ticks_per_beat (int): MIDI ticks per quarter note
        beats_per_bar (int): Beats per bar (4 for 4/4 time)
    
    Returns:
        int: Absolute tick position
    """
    # Convert to 0-based indexing
    bar_zero_based = bar - 1
    beat_zero_based = beat - 1
    
    # Calculate total ticks
    total_ticks = (bar_zero_based * beats_per_bar * ticks_per_beat) + (beat_zero_based * ticks_per_beat)
    return total_ticks


def create_tempo_and_markers_track(bpm, ticks_per_beat=480):
    """
    Creates the tempo and markers track (Track 0).
    
    Args:
        bpm (int): Beats per minute
        ticks_per_beat (int): MIDI ticks per quarter note
    
    Returns:
        mido.MidiTrack: Configured tempo and markers track
    """
    track = mido.MidiTrack()
    
    # Track name
    track.append(mido.MetaMessage('track_name', name='Tempo & Markers', time=0))
    
    # Set tempo (microseconds per beat)
    tempo = mido.bpm2tempo(bpm)
    track.append(mido.MetaMessage('set_tempo', tempo=tempo, time=0))
    
    # Set time signature (4/4)
    track.append(mido.MetaMessage('time_signature', numerator=4, denominator=4, 
                                 clocks_per_click=24, notated_32nd_notes_per_beat=8, time=0))
    
    # Add markers with correct delta times
    current_time = 0
    
    # Intro marker at Bar 1 (tick 0)
    intro_tick = bar_beat_to_ticks(1, 1, ticks_per_beat)
    delta_time = intro_tick - current_time
    track.append(mido.MetaMessage('marker', text='Intro', time=delta_time))
    current_time = intro_tick
    
    # Drop marker at Bar 5
    drop_tick = bar_beat_to_ticks(5, 1, ticks_per_beat)
    delta_time = drop_tick - current_time
    track.append(mido.MetaMessage('marker', text='Drop', time=delta_time))
    current_time = drop_tick
    
    # Outro marker at Bar 9
    outro_tick = bar_beat_to_ticks(9, 1, ticks_per_beat)
    delta_time = outro_tick - current_time
    track.append(mido.MetaMessage('marker', text='Outro', time=delta_time))
    current_time = outro_tick
    
    # End of track
    track.append(mido.MetaMessage('end_of_track', time=0))
    
    return track


def create_kick_track(ticks_per_beat=480):
    """
    Creates the KICK track with C1 notes on beat 1 of bars 1-4.
    
    Args:
        ticks_per_beat (int): MIDI ticks per quarter note
    
    Returns:
        mido.MidiTrack: Configured KICK track
    """
    track = mido.MidiTrack()
    
    # Track name
    track.append(mido.MetaMessage('track_name', name='KICK', time=0))
    
    # KICK pattern: C1 (MIDI note 36) on beat 1 of bars 1-4
    kick_note = 36  # C1
    kick_velocity = 120
    kick_channel = 0  # MIDI channel 0 (user-facing channel 1)
    note_duration = ticks_per_beat // 4  # 1/16th note duration
    
    current_time = 0
    
    for bar in range(1, 5):  # Bars 1-4
        # Calculate tick position for beat 1 of this bar
        note_start_tick = bar_beat_to_ticks(bar, 1, ticks_per_beat)
        
        # Delta time to note start
        delta_to_start = note_start_tick - current_time
        
        # Note ON
        track.append(mido.Message('note_on', channel=kick_channel, note=kick_note, 
                                 velocity=kick_velocity, time=delta_to_start))
        
        # Note OFF (after 1/16th note duration)
        track.append(mido.Message('note_off', channel=kick_channel, note=kick_note, 
                                 velocity=0, time=note_duration))
        
        current_time = note_start_tick + note_duration
    
    # End of track
    track.append(mido.MetaMessage('end_of_track', time=0))
    
    return track


def create_snare_track(ticks_per_beat=480):
    """
    Creates the SNARE track with D1 notes on beats 2 & 4 of bars 1-4.
    
    Args:
        ticks_per_beat (int): MIDI ticks per quarter note
    
    Returns:
        mido.MidiTrack: Configured SNARE track
    """
    track = mido.MidiTrack()
    
    # Track name
    track.append(mido.MetaMessage('track_name', name='SNARE', time=0))
    
    # SNARE pattern: D1 (MIDI note 38) on beats 2 & 4 of bars 1-4
    snare_note = 38  # D1
    snare_velocity = 110
    snare_channel = 1  # MIDI channel 1 (user-facing channel 2)
    note_duration = ticks_per_beat // 4  # 1/16th note duration
    
    current_time = 0
    
    for bar in range(1, 5):  # Bars 1-4
        for beat in [2, 4]:  # Beats 2 and 4
            # Calculate tick position for this beat
            note_start_tick = bar_beat_to_ticks(bar, beat, ticks_per_beat)
            
            # Delta time to note start
            delta_to_start = note_start_tick - current_time
            
            # Note ON
            track.append(mido.Message('note_on', channel=snare_channel, note=snare_note, 
                                     velocity=snare_velocity, time=delta_to_start))
            
            # Note OFF (after 1/16th note duration)
            track.append(mido.Message('note_off', channel=snare_channel, note=snare_note, 
                                     velocity=0, time=note_duration))
            
            current_time = note_start_tick + note_duration
    
    # End of track
    track.append(mido.MetaMessage('end_of_track', time=0))
    
    return track


def print_companion_notes(bpm, filename):
    """
    Prints companion notes to the console after MIDI file generation.
    
    Args:
        bpm (int): BPM used in the generated file
        filename (str): Name of the generated MIDI file
    """
    print(f"\nMIDI file '{filename}' created.")
    print("\nCompanion Notes (v0.0.1):")
    print(f"- BPM: {bpm}")
    print("- Song Markers:")
    print("  - Intro @ Bar 1")
    print("  - Drop @ Bar 5")
    print("  - Outro @ Bar 9")
    print("- Track 1: KICK (MIDI Channel 1)")
    print("  - Placeholder Note: C1 (MIDI Note 36)")
    print("  - Instruction: Load your main Kick sound. Pattern is on beat 1 for first 4 bars.")
    print("- Track 2: SNARE (MIDI Channel 2)")
    print("  - Placeholder Note: D1 (MIDI Note 38)")
    print("  - Instruction: Load your main Snare sound. Pattern is on beats 2 & 4 for first 4 bars.")


def generate_midi_file(bpm, filename="jumpup_template_v001.mid"):
    """
    Main function to generate the complete MIDI file.
    
    Args:
        bpm (int): Beats per minute
        filename (str): Output filename
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create MIDI file with standard settings
        ticks_per_beat = 480
        mid = mido.MidiFile(ticks_per_beat=ticks_per_beat)
        
        # Create and add tracks
        tempo_track = create_tempo_and_markers_track(bpm, ticks_per_beat)
        kick_track = create_kick_track(ticks_per_beat)
        snare_track = create_snare_track(ticks_per_beat)
        
        mid.tracks.append(tempo_track)
        mid.tracks.append(kick_track)
        mid.tracks.append(snare_track)
        
        # Save the MIDI file
        mid.save(filename)
        
        return True
        
    except Exception as e:
        print(f"Error generating MIDI file: {e}")
        return False


def main():
    """
    Main orchestration function.
    """
    print("=== MIDI-Maestro for Jump Up DnB - Prototype v0.0.1 ===")
    print("Generating basic Jump Up DnB MIDI template...\n")
    
    # Get BPM from user
    bpm = get_user_bpm()
    
    # Generate MIDI file
    filename = "jumpup_template_v001.mid"
    success = generate_midi_file(bpm, filename)
    
    if success:
        # Print companion notes
        print_companion_notes(bpm, filename)
        print(f"\nSuccess! Open '{filename}' in your DAW to start producing.")
    else:
        print("Failed to generate MIDI file. Please check for errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
