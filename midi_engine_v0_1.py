#!/usr/bin/env python3
"""
MIDI-Maestro for Jump Up DnB - Core MIDI Generation Engine v0.1
A flexible MIDI generation engine using data structures for musical elements.

This module provides the core functionality for generating MIDI files from
configuration data, supporting multiple tracks, markers, and event types.

Author: MIDI-Maestro Development Team
Version: 0.1
"""

import mido
from mido import Message, MidiFile, MidiTrack, MetaMessage, bpm2tempo
import json
from dataclasses import dataclass
from typing import List, Union, Dict, Any

# Constants
TICKS_PER_BEAT = 480
BEATS_PER_BAR = 4


# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

def bar_beat_to_ticks(bar: int, beat: float, beats_per_bar: int = BEATS_PER_BAR, 
                     ticks_per_beat: int = TICKS_PER_BEAT) -> int:
    """
    Converts bar.beat position to absolute MIDI ticks.
    
    Args:
        bar (int): Bar number (1-based)
        beat (float): Beat position within bar (1.0-based, e.g., 1.0, 1.25, 2.5)
        beats_per_bar (int): Number of beats per bar
        ticks_per_beat (int): MIDI ticks per quarter note
    
    Returns:
        int: Absolute tick position
    """
    # Convert to 0-based indexing
    bar_zero_based = bar - 1
    beat_zero_based = beat - 1.0
    
    # Calculate total ticks
    total_ticks = (bar_zero_based * beats_per_bar * ticks_per_beat) + (beat_zero_based * ticks_per_beat)
    return int(total_ticks)


def pitch_to_note_name(midi_note_number: int) -> str:
    """
    Converts MIDI note number to note name string.
    
    Args:
        midi_note_number (int): MIDI note number (0-127)
    
    Returns:
        str: Note name (e.g., "C1", "F#3", "Bb2")
    """
    note_names = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
    octave = (midi_note_number // 12) - 1
    note = note_names[midi_note_number % 12]
    return f"{note}{octave}"


# ============================================================================
# CORE DATA STRUCTURES
# ============================================================================

@dataclass
class SongConfig:
    """Configuration for overall song properties"""
    bpm: int
    key_signature: str
    song_length_bars: int


@dataclass
class MarkerInfo:
    """Information for a song structure marker"""
    name: str
    bar: int
    beat: float = 1.0


@dataclass
class NoteEventInfo:
    """Information for a MIDI note event"""
    bar: int
    beat: float
    pitch: int
    velocity: int
    duration_beats: float
    channel: int  # 0-15


@dataclass
class CCEventInfo:
    """Information for a MIDI control change event"""
    bar: int
    beat: float
    cc_num: int
    value: int
    channel: int  # 0-15


@dataclass
class TrackConfig:
    """Configuration for a single MIDI track"""
    track_name: str
    mido_channel: int  # 0-15
    instrument_type: str
    sound_design_notes: str
    events: List[Union[NoteEventInfo, CCEventInfo]]


# ============================================================================
# MAIN GENERATION FUNCTION
# ============================================================================

def generate_midi_from_config(song_config: SongConfig, 
                             structure_markers_data: List[MarkerInfo],
                             track_configs_data: List[TrackConfig],
                             output_filename: str) -> bool:
    """
    Generate a MIDI file from configuration data.
    
    Args:
        song_config: Song-level configuration (BPM, key, length)
        structure_markers_data: List of song structure markers
        track_configs_data: List of track configurations
        output_filename: Path for output MIDI file
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Initialize MIDI file
        mid = MidiFile(ticks_per_beat=TICKS_PER_BEAT)
        
        # Create Tempo & Markers track
        tempo_track = MidiTrack()
        tempo_track.append(MetaMessage('track_name', name='Tempo & Markers', time=0))
        
        # Set tempo
        tempo = bpm2tempo(song_config.bpm)
        tempo_track.append(MetaMessage('set_tempo', tempo=tempo, time=0))
        
        # Set time signature (4/4)
        tempo_track.append(MetaMessage('time_signature', 
                                     numerator=4, denominator=4,
                                     clocks_per_click=24, 
                                     notated_32nd_notes_per_beat=8, 
                                     time=0))
        
        # Add key signature if provided
        if song_config.key_signature:
            # Note: This is a simplified key signature implementation
            # Real implementation would parse the key string properly
            tempo_track.append(MetaMessage('key_signature', key=0, time=0))
        
        # Add markers with correct delta times
        current_time = 0
        for marker in structure_markers_data:
            marker_tick = bar_beat_to_ticks(marker.bar, marker.beat)
            delta_time = marker_tick - current_time
            tempo_track.append(MetaMessage('marker', text=marker.name, time=delta_time))
            current_time = marker_tick
        
        # End of tempo track
        tempo_track.append(MetaMessage('end_of_track', time=0))
        mid.tracks.append(tempo_track)
        
        # Create individual tracks
        for track_config in track_configs_data:
            track = MidiTrack()
            track.append(MetaMessage('track_name', name=track_config.track_name, time=0))
            
            # Sort events by time for proper delta calculation
            sorted_events = sorted(track_config.events, 
                                 key=lambda e: bar_beat_to_ticks(e.bar, e.beat))
            
            current_time = 0
            
            for event in sorted_events:
                event_tick = bar_beat_to_ticks(event.bar, event.beat)
                delta_time = event_tick - current_time
                
                if isinstance(event, NoteEventInfo):
                    # Note ON
                    track.append(Message('note_on',
                                       channel=event.channel,
                                       note=event.pitch,
                                       velocity=event.velocity,
                                       time=delta_time))
                    
                    # Note OFF
                    duration_ticks = int(event.duration_beats * TICKS_PER_BEAT)
                    track.append(Message('note_off',
                                       channel=event.channel,
                                       note=event.pitch,
                                       velocity=0,
                                       time=duration_ticks))
                    
                    current_time = event_tick + duration_ticks
                    
                elif isinstance(event, CCEventInfo):
                    # Control Change
                    track.append(Message('control_change',
                                       channel=event.channel,
                                       control=event.cc_num,
                                       value=event.value,
                                       time=delta_time))
                    
                    current_time = event_tick
            
            # End of track
            track.append(MetaMessage('end_of_track', time=0))
            mid.tracks.append(track)
        
        # Save MIDI file
        mid.save(output_filename)
        return True
        
    except Exception as e:
        print(f"Error generating MIDI file: {e}")
        return False


# ============================================================================
# JSON CONFIGURATION LOADING
# ============================================================================

def load_project_config_from_json(json_filepath: str) -> tuple:
    """
    Load project configuration from JSON file.

    Args:
        json_filepath (str): Path to the JSON configuration file

    Returns:
        tuple: (song_config, structure_markers_data, track_configs_data)

    Raises:
        FileNotFoundError: If JSON file doesn't exist
        ValueError: If JSON structure is invalid
    """
    try:
        with open(json_filepath, 'r') as f:
            data = json.load(f)

        # Parse song configuration
        song_data = data['song_config']
        song_config = SongConfig(
            bpm=song_data['bpm'],
            key_signature=song_data['key_signature'],
            song_length_bars=song_data['song_length_bars']
        )

        # Parse structure markers
        structure_markers_data = []
        for marker_data in data['structure_markers']:
            marker = MarkerInfo(
                name=marker_data['name'],
                bar=marker_data['bar'],
                beat=marker_data.get('beat', 1.0)
            )
            structure_markers_data.append(marker)

        # Parse track configurations
        track_configs_data = []
        for track_data in data['tracks']:
            events = []

            for event_data in track_data['events']:
                if event_data['type'] == 'note':
                    event = NoteEventInfo(
                        bar=event_data['bar'],
                        beat=event_data['beat'],
                        pitch=event_data['pitch'],
                        velocity=event_data['velocity'],
                        duration_beats=event_data['duration_beats'],
                        channel=event_data['channel']
                    )
                elif event_data['type'] == 'cc':
                    event = CCEventInfo(
                        bar=event_data['bar'],
                        beat=event_data['beat'],
                        cc_num=event_data['cc_num'],
                        value=event_data['value'],
                        channel=event_data['channel']
                    )
                else:
                    continue  # Skip unknown event types

                events.append(event)

            track_config = TrackConfig(
                track_name=track_data['track_name'],
                mido_channel=track_data['mido_channel'],
                instrument_type=track_data['instrument_type'],
                sound_design_notes=track_data['sound_design_notes'],
                events=events
            )
            track_configs_data.append(track_config)

        return song_config, structure_markers_data, track_configs_data

    except FileNotFoundError:
        raise FileNotFoundError(f"Configuration file '{json_filepath}' not found")
    except KeyError as e:
        raise ValueError(f"Missing required key in JSON configuration: {e}")
    except Exception as e:
        raise ValueError(f"Error parsing JSON configuration: {e}")


def generate_companion_notes(song_config: SongConfig,
                           structure_markers_data: List[MarkerInfo],
                           track_configs_data: List[TrackConfig]) -> str:
    """
    Generate formatted companion notes for the project.

    Args:
        song_config: Song configuration
        structure_markers_data: List of structure markers
        track_configs_data: List of track configurations

    Returns:
        str: Formatted companion notes
    """
    notes = []
    notes.append("🎼 MIDI-Maestro Jump Up DnB - Project Companion Notes")
    notes.append("=" * 60)
    notes.append("")

    # Song information
    notes.append("📊 Song Configuration:")
    notes.append(f"   • BPM: {song_config.bpm}")
    notes.append(f"   • Key Signature: {song_config.key_signature}")
    notes.append(f"   • Length: {song_config.song_length_bars} bars")
    notes.append("")

    # Structure markers
    notes.append("🏗️  Song Structure Markers:")
    for marker in structure_markers_data:
        beat_str = f".{int((marker.beat - 1) * 4) + 1}" if marker.beat != 1.0 else ""
        notes.append(f"   • {marker.name} @ Bar {marker.bar}{beat_str}")
    notes.append("")

    # Track information
    notes.append("🎵 Track Information:")
    for track in track_configs_data:
        user_channel = track.mido_channel + 1  # Convert to user-facing channel
        notes.append(f"   Track: {track.track_name}")
        notes.append(f"      └─ MIDI Channel: {user_channel}")
        notes.append(f"      └─ Instrument Type: {track.instrument_type}")
        notes.append(f"      └─ Sound Design Notes: {track.sound_design_notes}")

        # Count events by type
        note_events = sum(1 for e in track.events if isinstance(e, NoteEventInfo))
        cc_events = sum(1 for e in track.events if isinstance(e, CCEventInfo))
        notes.append(f"      └─ Events: {note_events} note events, {cc_events} CC events")
        notes.append("")

    notes.append("🎛️  Production Tips:")
    notes.append("   1. Load appropriate Jump Up DnB samples for each track")
    notes.append("   2. Use the structure markers to arrange your track")
    notes.append("   3. Layer additional elements during the drop sections")
    notes.append("   4. Apply sidechain compression from kick to bass")
    notes.append("   5. Add reverb and delay effects for depth")
    notes.append("")
    notes.append("🚀 Happy producing!")
    notes.append("=" * 60)

    return "\n".join(notes)


# ============================================================================
# EXAMPLE USAGE AND TESTING
# ============================================================================

def create_sample_config_json(filename: str = "config.json"):
    """Create a sample configuration JSON file for testing"""
    sample_config = {
        "song_config": {
            "bpm": 174,
            "key_signature": "F Minor",
            "song_length_bars": 16
        },
        "structure_markers": [
            {"name": "Intro", "bar": 1, "beat": 1.0},
            {"name": "Build", "bar": 5, "beat": 1.0},
            {"name": "Drop", "bar": 9, "beat": 1.0},
            {"name": "Outro", "bar": 13, "beat": 1.0}
        ],
        "tracks": [
            {
                "track_name": "KICK_Main",
                "mido_channel": 0,
                "instrument_type": "Kick",
                "sound_design_notes": "Punchy kick drum with sub-bass content",
                "events": [
                    {"type": "note", "bar": 9, "beat": 1.0, "pitch": 36, "velocity": 127, "duration_beats": 0.25, "channel": 0},
                    {"type": "note", "bar": 9, "beat": 2.75, "pitch": 36, "velocity": 120, "duration_beats": 0.25, "channel": 0},
                    {"type": "note", "bar": 9, "beat": 4.5, "pitch": 36, "velocity": 115, "duration_beats": 0.25, "channel": 0},
                    {"type": "note", "bar": 10, "beat": 1.0, "pitch": 36, "velocity": 127, "duration_beats": 0.25, "channel": 0}
                ]
            },
            {
                "track_name": "SNARE_Main",
                "mido_channel": 1,
                "instrument_type": "Snare",
                "sound_design_notes": "Crisp snare with good snap and presence",
                "events": [
                    {"type": "note", "bar": 9, "beat": 2.0, "pitch": 38, "velocity": 120, "duration_beats": 0.25, "channel": 1},
                    {"type": "note", "bar": 9, "beat": 4.0, "pitch": 38, "velocity": 115, "duration_beats": 0.25, "channel": 1},
                    {"type": "note", "bar": 10, "beat": 2.0, "pitch": 38, "velocity": 120, "duration_beats": 0.25, "channel": 1},
                    {"type": "note", "bar": 10, "beat": 4.0, "pitch": 38, "velocity": 115, "duration_beats": 0.25, "channel": 1}
                ]
            }
        ]
    }

    with open(filename, 'w') as f:
        json.dump(sample_config, f, indent=2)

    print(f"✅ Sample configuration saved to '{filename}'")


if __name__ == '__main__':
    # Create sample config if it doesn't exist
    config_filename = "config.json"

    try:
        # Try to load existing config
        song_config, structure_markers_data, track_configs_data = load_project_config_from_json(config_filename)
        print(f"✅ Loaded configuration from '{config_filename}'")
    except FileNotFoundError:
        # Create sample config if file doesn't exist
        print(f"⚠️  Configuration file '{config_filename}' not found. Creating sample...")
        create_sample_config_json(config_filename)
        song_config, structure_markers_data, track_configs_data = load_project_config_from_json(config_filename)
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        exit(1)

    # Generate MIDI file
    output_filename = "jumpup_engine_test_v0_1.mid"
    print(f"\n🎵 Generating MIDI file...")
    success = generate_midi_from_config(song_config, structure_markers_data,
                                      track_configs_data, output_filename)

    if success:
        print(f"✅ MIDI file '{output_filename}' generated successfully!")

        # Generate and display companion notes
        companion_notes = generate_companion_notes(song_config, structure_markers_data, track_configs_data)
        print(f"\n{companion_notes}")

    else:
        print("❌ Failed to generate MIDI file")
