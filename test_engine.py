#!/usr/bin/env python3
"""
Test script for MIDI Engine v0.1
"""

import sys
import os

def test_engine():
    """Test the MIDI engine step by step"""
    print("🧙‍♂️ MIDI-Maestro Wizard Testing Engine...")
    print("=" * 50)
    
    try:
        # Test 1: Import the engine
        print("📦 Test 1: Importing MIDI engine...")
        import midi_engine_v0_1 as engine
        print("✅ Engine imported successfully!")
        
        # Test 2: Check if config.json exists
        print("\n📄 Test 2: Checking config.json...")
        if os.path.exists("config.json"):
            print("✅ config.json found!")
        else:
            print("⚠️  config.json not found, will create one...")
        
        # Test 3: Load configuration
        print("\n🔧 Test 3: Loading configuration...")
        try:
            song_config, markers, tracks = engine.load_project_config_from_json("config.json")
            print(f"✅ Configuration loaded!")
            print(f"   📊 BPM: {song_config.bpm}")
            print(f"   🎵 Key: {song_config.key_signature}")
            print(f"   📏 Length: {song_config.song_length_bars} bars")
            print(f"   🏗️  Markers: {len(markers)}")
            print(f"   🎛️  Tracks: {len(tracks)}")
        except Exception as e:
            print(f"❌ Config loading failed: {e}")
            print("🔧 Creating sample config...")
            engine.create_sample_config_json("config.json")
            song_config, markers, tracks = engine.load_project_config_from_json("config.json")
            print("✅ Sample config created and loaded!")
        
        # Test 4: Generate MIDI
        print("\n🎵 Test 4: Generating MIDI file...")
        output_file = "test_output.mid"
        success = engine.generate_midi_from_config(song_config, markers, tracks, output_file)
        
        if success:
            print(f"✅ MIDI file '{output_file}' generated successfully!")
            
            # Check file size
            if os.path.exists(output_file):
                size = os.path.getsize(output_file)
                print(f"   📁 File size: {size} bytes")
                if size > 0:
                    print("✅ File has content!")
                else:
                    print("❌ File is empty!")
            
        else:
            print("❌ MIDI generation failed!")
            return False
        
        # Test 5: Generate companion notes
        print("\n📝 Test 5: Generating companion notes...")
        try:
            notes = engine.generate_companion_notes(song_config, markers, tracks)
            print("✅ Companion notes generated!")
            print("\n" + "="*60)
            print(notes)
            print("="*60)
        except Exception as e:
            print(f"❌ Companion notes failed: {e}")
        
        print(f"\n🎉 ALL TESTS PASSED! Engine is working perfectly!")
        return True
        
    except Exception as e:
        print(f"❌ Critical error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_engine()
    if success:
        print("\n🚀 Ready to proceed to next phase!")
    else:
        print("\n🔧 Need to fix issues before proceeding...")
        sys.exit(1)
